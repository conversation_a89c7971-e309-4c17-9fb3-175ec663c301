<template>
<view class="content">
	<view class="swiper-container" :style='{"width":"100%","padding":"20rpx 2%","position":"relative","background":"linear-gradient(135deg, #E8F4FD 0%, #B3D9F2 100%)","height":"auto"}'>
		<!-- 轮播图标题 -->
		<view class="swiper-header" :style='{"display":"flex","alignItems":"center","marginBottom":"20rpx","padding":"0 10rpx"}'>
			<view class="health-icon" :style='{"width":"6rpx","height":"40rpx","background":"linear-gradient(180deg, #3B82F6 0%, #1E40AF 100%)","borderRadius":"3rpx","marginRight":"16rpx"}'></view>
			<text :style='{"fontSize":"36rpx","fontWeight":"600","color":"#1E40AF","letterSpacing":"1rpx"}'>健康资讯</text>
			<view class="hot-badge" :style='{"marginLeft":"16rpx","background":"rgba(59, 130, 246, 0.1)","padding":"4rpx 12rpx","borderRadius":"12rpx"}'>
				<text :style='{"fontSize":"24rpx","color":"#3B82F6","fontWeight":"500"}'>HOT</text>
			</view>
		</view>

		<swiper :style='{"width":"100%","overflow":"hidden","borderRadius":"24rpx","background":"#fff","height":"420rpx","boxShadow":"0 12rpx 40rpx rgba(59, 130, 246, 0.15), 0 4rpx 16rpx rgba(59, 130, 246, 0.1)","border":"3rpx solid rgba(59, 130, 246, 0.08)"}' class="medical-swiper" :indicator-dots='true' :autoplay='true' :circular='true' indicator-active-color='#3B82F6' indicator-color='rgba(59, 130, 246, 0.3)' :duration='800' :interval='4500' :vertical='false'>
			<swiper-item :style='{"width":"100%","background":"#fff","height":"420rpx","position":"relative","overflow":"hidden"}' v-for="(swiper,index) in swiperList" :key="index" @tap="onSwiperTap(swiper)">
				<image :style='{"width":"100%","objectFit":"cover","display":"block","height":"420rpx","borderRadius":"21rpx","transition":"transform 0.5s ease"}' mode="aspectFill" :src="baseUrl+swiper.img"></image>

				<!-- 渐变遮罩 -->
				<view :style='{"position":"absolute","top":"0","left":"0","width":"100%","height":"100%","background":"linear-gradient(180deg, rgba(0,0,0,0.1) 0%, transparent 30%, transparent 60%, rgba(0,0,0,0.8) 100%)","borderRadius":"21rpx"}'></view>

				<!-- 医疗图标装饰 -->
				<view :style='{"position":"absolute","top":"20rpx","left":"20rpx","width":"60rpx","height":"60rpx","background":"rgba(255, 255, 255, 0.9)","borderRadius":"30rpx","display":"flex","alignItems":"center","justifyContent":"center","boxShadow":"0 4rpx 12rpx rgba(0,0,0,0.1)"}'>
					<text class="iconfont icon-yiyuan" :style='{"fontSize":"32rpx","color":"#3B82F6"}'></text>
				</view>

				<!-- 医疗标签 -->
				<view class="medical-tag" :style='{"position":"absolute","top":"20rpx","right":"20rpx","background":"rgba(59, 130, 246, 0.95)","color":"#fff","padding":"10rpx 20rpx","borderRadius":"25rpx","fontSize":"24rpx","fontWeight":"600","backdropFilter":"blur(10rpx)","boxShadow":"0 4rpx 12rpx rgba(59, 130, 246, 0.3)"}'>
					<text class="iconfont icon-jiankang" :style='{"fontSize":"20rpx","marginRight":"8rpx"}'></text>
					医疗健康
				</view>

				<!-- 标题和描述 -->
				<view :style='{"position":"absolute","bottom":"0","left":"0","right":"0","padding":"30rpx","background":"linear-gradient(180deg, transparent 0%, rgba(0,0,0,0.9) 100%)","borderRadius":"0 0 21rpx 21rpx"}'>
					<view v-if="swiper.title" class="swiper-title" :style='{"color":"#fff","fontSize":"34rpx","fontWeight":"700","lineHeight":"1.4","textShadow":"0 2rpx 8rpx rgba(0,0,0,0.5)","marginBottom":"12rpx","letterSpacing":"0.5rpx"}'>{{ swiper.title }}</view>
					<view :style='{"display":"flex","alignItems":"center","justifyContent":"space-between"}'>
						<view :style='{"display":"flex","alignItems":"center"}'>
							<text class="iconfont icon-shijian" :style='{"fontSize":"24rpx","color":"rgba(255,255,255,0.8)","marginRight":"8rpx"}'></text>
							<text :style='{"fontSize":"24rpx","color":"rgba(255,255,255,0.8)"}'>刚刚更新</text>
						</view>
						<view :style='{"background":"rgba(255,255,255,0.2)","padding":"6rpx 16rpx","borderRadius":"15rpx","backdropFilter":"blur(5rpx)"}'>
							<text :style='{"fontSize":"22rpx","color":"#fff","fontWeight":"500"}'>查看详情</text>
						</view>
					</view>
				</view>

				<!-- 进度指示器 -->
				<view :style='{"position":"absolute","bottom":"20rpx","left":"30rpx","width":"60rpx","height":"4rpx","background":"rgba(255,255,255,0.3)","borderRadius":"2rpx","overflow":"hidden"}'>
					<view :style='{"width":"30%","height":"100%","background":"#fff","borderRadius":"2rpx","transition":"width 0.3s ease"}'></view>
				</view>
			</swiper-item>
		</swiper>

		<!-- 轮播图底部装饰 -->
		<view class="swiper-footer" :style='{"display":"flex","justifyContent":"center","alignItems":"center","marginTop":"20rpx","padding":"0 10rpx"}'>
			<view class="decoration-line" :style='{"flex":"1","height":"2rpx","background":"linear-gradient(90deg, transparent 0%, rgba(59, 130, 246, 0.3) 50%, transparent 100%)"}'></view>
			<view class="slide-hint" :style='{"margin":"0 20rpx","padding":"8rpx 20rpx","background":"rgba(255,255,255,0.8)","borderRadius":"20rpx","backdropFilter":"blur(10rpx)","border":"1rpx solid rgba(59, 130, 246, 0.1)"}'>
				<text :style='{"fontSize":"24rpx","color":"#3B82F6","fontWeight":"500"}'>滑动查看更多</text>
			</view>
			<view class="decoration-line" :style='{"flex":"1","height":"2rpx","background":"linear-gradient(90deg, transparent 0%, rgba(59, 130, 246, 0.3) 50%, transparent 100%)"}'></view>
		</view>
	</view>
		<!-- menu -->
		<view v-if="true" class="menu" :style='{"width":"100%","padding":"0","margin":"0","flexWrap":"wrap","display":"flex","height":"auto"}'>
            <block v-for="(item,index1) in menuList" v-bind:key="item.roleName">
                <block v-if="index1==0" v-bind:key="`menu_${index1}_${index}`" v-for=" (menu,index) in item.frontMenu">
                    <block v-bind:key="`child_${index1}_${index}_${sort}`" v-for=" (child,sort) in menu.child">
                        <block v-bind:key="`button_${index1}_${index}_${sort}_${sort2}`" v-for=" (button,sort2) in child.buttons">
                            <view :style='{"width":"23%","padding":"12rpx 0","margin":"10rpx 1%","height":"auto"}' class="menu-list" v-if="button=='查看' && child.tableName!='yifahuodingdan' && child.tableName!='yituikuandingdan' &&child.tableName!='yiquxiaodingdan' && child.tableName!='weizhifudingdan' && child.tableName!='yizhifudingdan' && child.tableName!='yiwanchengdingdan' " @tap="onPageTap2(child.tableName)">
                                <view class="iconarr" :class="child.appFrontIcon" :style='{"padding":"0","margin":"0px auto","color":"#333","borderRadius":"10rpx","textAlign":"center","background":"#D4CF5D","display":"block","width":"100rpx","lineHeight":"100rpx","fontSize":"64rpx","height":"100rpx"}'></view>
                                <view :style='{"padding":"0","margin":"12rpx auto 0","color":"#333","textAlign":"center","width":"100%","lineHeight":"28rpx","fontSize":"28rpx"}'>{{child.menu.split("列表")[0]}}</view>
                            </view>
                        </block>
                    </block>
                </block>
            </block>
		</view>
		<!-- menu -->
		<!-- 商品推荐 -->
		<!-- 商品推荐 -->
		
		<!-- 商品列表 -->
		<view class="listBox list" :style='{"width":"100%","margin":"0 0 20rpx","overflow":"hidden","borderRadius":"20rpx","background":"#fff"}'>
			<view class="title" :style='{"width":"100%","padding":"0 24rpx","margin":"0","background":"#D4CF5D","justifyContent":"space-between","display":"flex"}'>
				<view :style='{"color":"#fff","fontSize":"48rpx","lineHeight":"88rpx"}'>坐诊医生</view>
				<view :style='{"alignItems":"center","justifyContent":"center","display":"flex"}' @tap="onPageTap('zuozhenyisheng')">
				  <text :style='{"color":"#fff","fontSize":"28rpx"}'>更多</text>
				  <text class="icon iconfont icon-gengduo1" :style='{"color":"#fff","fontSize":"28rpx"}'></text>
				</view>
			</view>
		  <!-- 样式4 -->
		  <view v-if="4 == 4" class="list-box style4" :style='{"width":"100%","padding":"24rpx","margin":"0","height":"auto"}'>
			<view class="list-item" :style='{"width":"100%","padding":"0","margin":"0 0 20rpx","justifyContent":"space-between","display":"flex","height":"auto"}'>
			  <view v-if="homezuozhenyishenglist.length > 0" @tap="onDetailTap('zuozhenyisheng',homezuozhenyishenglist[0].id)" class="box box1" :style='{"padding":"0","margin":"0","overflow":"hidden","borderRadius":"20rpx","width":"60%","position":"relative","height":"auto"}'>
				<image :style='{"width":"100%","objectFit":"cover","display":"block","height":"672rpx"}' class="list-item-image" mode="aspectFill" v-if="homezuozhenyishenglist[0].zhaopian.substring(0,4)=='http'" :src="homezuozhenyishenglist[0].zhaopian"></image>
				<image :style='{"width":"100%","objectFit":"cover","display":"block","height":"672rpx"}' class="list-item-image" mode="aspectFill" v-else :src="homezuozhenyishenglist[0].zhaopian?baseUrl+homezuozhenyishenglist[0].zhaopian.split(',')[0]:''"></image>
				<view :style='{"width":"100%","position":"absolute","left":"0","bottom":"0","background":"rgba(223,216,204,0.7)"}'>
					<view :style='{"padding":"0 20rpx","lineHeight":"1.5","fontSize":"32rpx","color":"#000000"}' class="title ">{{homezuozhenyishenglist[0].yishengxingming}}</view>
					<view :style='{"padding":"0 20rpx","display":"none"}'>
					  <text class="icon iconfont icon-shijian21" :style='{"margin":"0 4rpx 0 0","lineHeight":"1.5","fontSize":"24rpx","color":"#000000"}'></text>
					  <text :style='{"color":"#000000","lineHeight":"1.5","fontSize":"24rpx"}'>{{homezuozhenyishenglist[0].addtime}}</text>
					</view>
					<view :style='{"padding":"0 20rpx","display":"none"}'>
					  <text class="icon iconfont icon-shoucang10" :style='{"margin":"0 4rpx 0 0","lineHeight":"1.5","fontSize":"24rpx","color":"#000000"}'></text>
					  <text :style='{"color":"#000000","lineHeight":"1.5","fontSize":"24rpx"}'>{{homezuozhenyishenglist[0].storeupnum}}</text>
					</view>
				</view>
			  </view>
			  <view class="list-item-body" :style='{"width":"38%","padding":"0","margin":"0","height":"auto"}'>
				<view v-if="homezuozhenyishenglist.length > 1" @tap="onDetailTap('zuozhenyisheng',homezuozhenyishenglist[1].id)" class="box box2" :style='{"padding":"0","margin":"0 0 20rpx","overflow":"hidden","borderRadius":"20rpx","width":"100%","position":"relative","height":"auto"}'>
				  <image :style='{"width":"100%","objectFit":"cover","display":"block","height":"328rpx"}' class="list-item-image" mode="aspectFill" v-if="homezuozhenyishenglist[1].zhaopian.substring(0,4)=='http'" :src="homezuozhenyishenglist[1].zhaopian"></image>
				  <image :style='{"width":"100%","objectFit":"cover","display":"block","height":"328rpx"}' class="list-item-image" mode="aspectFill" v-else :src="homezuozhenyishenglist[1].zhaopian?baseUrl+homezuozhenyishenglist[1].zhaopian.split(',')[0]:''"></image>
				  <view :style='{"width":"100%","position":"absolute","left":"0","bottom":"0","background":"rgba(223,216,204,0.7)"}'>
					<view :style='{"padding":"0 20rpx","lineHeight":"1.5","fontSize":"32rpx","color":"#000000"}' class="title ">{{homezuozhenyishenglist[1].yishengxingming}}</view>
					<view :style='{"padding":"0 20rpx","display":"none"}'>
					  <text class="icon iconfont icon-shijian21" :style='{"margin":"0 4rpx 0 0","lineHeight":"1.5","fontSize":"24rpx","color":"#000000"}'></text>
					  <text :style='{"color":"#000000","lineHeight":"1.5","fontSize":"24rpx"}'>{{homezuozhenyishenglist[1].addtime}}</text>
					</view>
					<view :style='{"padding":"0 20rpx","display":"none"}'>
					  <text class="icon iconfont icon-shoucang10" :style='{"margin":"0 4rpx 0 0","lineHeight":"1.5","fontSize":"24rpx","color":"#000000"}'></text>
					  <text :style='{"color":"#000000","lineHeight":"1.5","fontSize":"24rpx"}'>{{homezuozhenyishenglist[1].storeupnum}}</text>
					</view>
				  </view>
				</view>
				<view v-if="homezuozhenyishenglist.length > 2" @tap="onDetailTap('zuozhenyisheng',homezuozhenyishenglist[2].id)" class="box box3" :style='{"padding":"0","margin":"0","overflow":"hidden","borderRadius":"20rpx","width":"100%","position":"relative","height":"auto"}'>
				  <image :style='{"width":"100%","objectFit":"cover","display":"block","height":"328rpx"}' class="list-item-image" mode="aspectFill" v-if="homezuozhenyishenglist[2].zhaopian.substring(0,4)=='http'" :src="homezuozhenyishenglist[2].zhaopian"></image>
				  <image :style='{"width":"100%","objectFit":"cover","display":"block","height":"328rpx"}' class="list-item-image" mode="aspectFill" v-else :src="homezuozhenyishenglist[2].zhaopian?baseUrl+homezuozhenyishenglist[2].zhaopian.split(',')[0]:''"></image>
				  <view :style='{"width":"100%","position":"absolute","left":"0","bottom":"0","background":"rgba(223,216,204,0.7)"}'>
					<view :style='{"padding":"0 20rpx","lineHeight":"1.5","fontSize":"32rpx","color":"#000000"}' class="title ">{{homezuozhenyishenglist[2].yishengxingming}}</view>
					<view :style='{"padding":"0 20rpx","display":"none"}'>
					  <text class="icon iconfont icon-shijian21" :style='{"margin":"0 4rpx 0 0","lineHeight":"1.5","fontSize":"24rpx","color":"#000000"}'></text>
					  <text :style='{"color":"#000000","lineHeight":"1.5","fontSize":"24rpx"}'>{{homezuozhenyishenglist[2].addtime}}</text>
					</view>
					<view :style='{"padding":"0 20rpx","display":"none"}'>
					  <text class="icon iconfont icon-shoucang10" :style='{"margin":"0 4rpx 0 0","lineHeight":"1.5","fontSize":"24rpx","color":"#000000"}'></text>
					  <text :style='{"color":"#000000","lineHeight":"1.5","fontSize":"24rpx"}'>{{homezuozhenyishenglist[2].storeupnum}}</text>
					</view>
				  </view>
				</view>
			  </view>
			</view>
			<view class="list-item" :style='{"width":"100%","padding":"0","margin":"0 0 20rpx","justifyContent":"space-between","display":"flex","height":"auto"}'>
			  <view v-if="homezuozhenyishenglist.length > 3" @tap="onDetailTap('zuozhenyisheng',homezuozhenyishenglist[3].id)" class="box box4" :style='{"padding":"0","margin":"0","overflow":"hidden","borderRadius":"20rpx","width":"60%","position":"relative","height":"auto"}'>
				<image :style='{"width":"100%","objectFit":"cover","display":"block","height":"328rpx"}' class="list-item-image" mode="aspectFill" v-if="homezuozhenyishenglist[3].zhaopian.substring(0,4)=='http'" :src="homezuozhenyishenglist[3].zhaopian"></image>
				<image :style='{"width":"100%","objectFit":"cover","display":"block","height":"328rpx"}' class="list-item-image" mode="aspectFill" v-else :src="homezuozhenyishenglist[3].zhaopian?baseUrl+homezuozhenyishenglist[3].zhaopian.split(',')[0]:''"></image>
				<view :style='{"width":"100%","position":"absolute","left":"0","bottom":"0","background":"rgba(223,216,204,0.7)"}'>
					<view :style='{"padding":"0 20rpx","lineHeight":"1.5","fontSize":"32rpx","color":"#000000"}' class="title ">{{homezuozhenyishenglist[3].yishengxingming}}</view>
					<view :style='{"padding":"0 20rpx","display":"none"}'>
					  <text class="icon iconfont icon-shijian21" :style='{"margin":"0 4rpx 0 0","lineHeight":"1.5","fontSize":"24rpx","color":"#000000"}'></text>
					  <text :style='{"color":"#000000","lineHeight":"1.5","fontSize":"24rpx"}'>{{homezuozhenyishenglist[3].addtime}}</text>
					</view>
					<view :style='{"padding":"0 20rpx","display":"none"}'>
					  <text class="icon iconfont icon-shoucang10" :style='{"margin":"0 4rpx 0 0","lineHeight":"1.5","fontSize":"24rpx","color":"#000000"}'></text>
					  <text :style='{"color":"#000000","lineHeight":"1.5","fontSize":"24rpx"}'>{{homezuozhenyishenglist[3].storeupnum}}</text>
					</view>
				</view>
			  </view>
			  <view v-if="homezuozhenyishenglist.length > 4" @tap="onDetailTap('zuozhenyisheng',homezuozhenyishenglist[4].id)" class="box box5" :style='{"padding":"0","margin":"0","overflow":"hidden","borderRadius":"20rpx","width":"38%","position":"relative","height":"auto"}'>
				<image :style='{"width":"100%","objectFit":"cover","display":"block","height":"328rpx"}' class="list-item-image" mode="aspectFill" v-if="homezuozhenyishenglist[4].zhaopian.substring(0,4)=='http'" :src="homezuozhenyishenglist[4].zhaopian"></image>
				<image :style='{"width":"100%","objectFit":"cover","display":"block","height":"328rpx"}' class="list-item-image" mode="aspectFill" v-else :src="homezuozhenyishenglist[4].zhaopian?baseUrl+homezuozhenyishenglist[4].zhaopian.split(',')[0]:''"></image>
				<view :style='{"width":"100%","position":"absolute","left":"0","bottom":"0","background":"rgba(223,216,204,0.7)"}'>
					<view :style='{"padding":"0 20rpx","lineHeight":"1.5","fontSize":"32rpx","color":"#000000"}' class="title ">{{homezuozhenyishenglist[4].yishengxingming}}</view>
					<view :style='{"padding":"0 20rpx","display":"none"}'>
					  <text class="icon iconfont icon-shijian21" :style='{"margin":"0 4rpx 0 0","lineHeight":"1.5","fontSize":"24rpx","color":"#000000"}'></text>
					  <text :style='{"color":"#000000","lineHeight":"1.5","fontSize":"24rpx"}'>{{homezuozhenyishenglist[4].addtime}}</text>
					</view>
					<view :style='{"padding":"0 20rpx","display":"none"}'>
					  <text class="icon iconfont icon-shoucang10" :style='{"margin":"0 4rpx 0 0","lineHeight":"1.5","fontSize":"24rpx","color":"#000000"}'></text>
					  <text :style='{"color":"#000000","lineHeight":"1.5","fontSize":"24rpx"}'>{{homezuozhenyishenglist[4].storeupnum}}</text>
					</view>
				</view>
			  </view>
			</view>
			<view v-if="homezuozhenyishenglist.length > 5" @tap="onDetailTap('zuozhenyisheng',homezuozhenyishenglist[5].id)" class="box box6" :style='{"padding":"0","margin":"0","overflow":"hidden","borderRadius":"20rpx","width":"100%","position":"relative","height":"auto"}'>
			  <image :style='{"width":"100%","objectFit":"cover","display":"block","height":"328rpx"}' class="list-item-image" mode="aspectFill" v-if="homezuozhenyishenglist[5].zhaopian.substring(0,4)=='http'" :src="homezuozhenyishenglist[5].zhaopian"></image>
			  <image :style='{"width":"100%","objectFit":"cover","display":"block","height":"328rpx"}' class="list-item-image" mode="aspectFill" v-else :src="homezuozhenyishenglist[5].zhaopian?baseUrl+homezuozhenyishenglist[5].zhaopian.split(',')[0]:''"></image>
				<view :style='{"width":"100%","position":"absolute","left":"0","bottom":"0","background":"rgba(223,216,204,0.7)"}'>
					<view :style='{"padding":"0 20rpx","lineHeight":"1.5","fontSize":"32rpx","color":"#000000"}' class="title ">{{homezuozhenyishenglist[5].yishengxingming}}</view>
					<view :style='{"padding":"0 20rpx","display":"none"}'>
					  <text class="icon iconfont icon-shijian21" :style='{"margin":"0 4rpx 0 0","lineHeight":"1.5","fontSize":"24rpx","color":"#000000"}'></text>
					  <text :style='{"color":"#000000","lineHeight":"1.5","fontSize":"24rpx"}'>{{homezuozhenyishenglist[5].addtime}}</text>
					</view>
					<view :style='{"padding":"0 20rpx","display":"none"}'>
					  <text class="icon iconfont icon-shoucang10" :style='{"margin":"0 4rpx 0 0","lineHeight":"1.5","fontSize":"24rpx","color":"#000000"}'></text>
					  <text :style='{"color":"#000000","lineHeight":"1.5","fontSize":"24rpx"}'>{{homezuozhenyishenglist[5].storeupnum}}</text>
					</view>
				</view>
			</view>
		  </view>
		</view>
		<!-- 商品列表 -->
		<!-- 新闻资讯 -->
		<view class="listBox news" :style='{"width":"100%","margin":"0 0 20rpx","overflow":"hidden","borderRadius":"20rpx","background":"#fff"}'>
			<view class="title" :style='{"width":"100%","padding":"0 24rpx","margin":"0","background":"#D4CF5D","justifyContent":"space-between","display":"flex"}'>
				<view :style='{"color":"#fff","fontSize":"48rpx","lineHeight":"88rpx"}'>公告信息</view>
				<view :style='{"alignItems":"center","justifyContent":"center","display":"flex"}' @tap="onPageTap('news')">
				  <text :style='{"color":"#fff","fontSize":"28rpx"}'>更多</text>
				  <text class="icon iconfont icon-gengduo1" :style='{"color":"#fff","fontSize":"28rpx"}'></text>
				</view>
			</view>
		  <!-- 样式5 -->
		  <view class="news-box2" :style='{"width":"100%","padding":"24rpx","margin":"0","height":"auto"}'>
			<block v-for="(item,index) in news" :key="index">
			  <view @tap="onNewsDetailTap(item.id)" v-if="index==0" class="list-item" :style='{"padding":"0","margin":"0 0 20rpx","overflow":"hidden","borderRadius":"20rpx","width":"100%","position":"relative","height":"auto"}'>
				<image :style='{"width":"100%","objectFit":"cover","display":"block","height":"400rpx"}' mode="aspectFill" class="listmpic" :src="baseUrl+item.picture"></image>
				<view :style='{"width":"100%","position":"absolute","left":"0","bottom":"0","background":"rgba(0,0,0,.3)"}'>
					<view :style='{"padding":"0 20rpx","lineHeight":"1.5","fontSize":"32rpx","color":"#fff"}' class="title">{{item.title}}</view>
					<view :style='{"padding":"0 20rpx","margin":"0","overflow":"hidden","color":"#fff","width":"100%","lineHeight":"50rpx","fontSize":"28rpx","height":"50rpx"}' class="text">{{item.introduction}}</view>
					<view :style='{"padding":"0 20rpx"}'>
					  <text class="icon iconfont icon-shijian21" :style='{"margin":"0 4rpx 0 0","lineHeight":"1.5","fontSize":"24rpx","color":"#fff"}'></text>
					  <text :style='{"color":"#fff","lineHeight":"1.5","fontSize":"24rpx"}'>{{item.addtime}}</text>
					</view>
					<view :style='{"padding":"0 20rpx"}'>
					  <text class="icon iconfont icon-geren16" :style='{"margin":"0 4rpx 0 0","lineHeight":"1.5","fontSize":"24rpx","color":"#fff"}'></text>
					  <text :style='{"color":"#fff","lineHeight":"1.5","fontSize":"24rpx"}'>{{item.name}}</text>
					</view>
					<view :style='{"padding":"0 20rpx","display":"inline-block"}'>
					  <text class="icon iconfont icon-zan10" :style='{"margin":"0 4rpx 0 0","lineHeight":"1.5","fontSize":"24rpx","color":"#fff"}'></text>
					  <text :style='{"color":"#fff","lineHeight":"1.5","fontSize":"24rpx"}'>{{item.thumbsupnum}}</text>
					</view>
					<view :style='{"padding":"0 20rpx","display":"inline-block"}'>
					  <text class="icon iconfont icon-shoucang10" :style='{"margin":"0 4rpx 0 0","lineHeight":"1.5","fontSize":"24rpx","color":"#fff"}'></text>
					  <text :style='{"color":"#fff","lineHeight":"1.5","fontSize":"24rpx"}'>{{item.storeupnum}}</text>
					</view>
					<view :style='{"padding":"0 20rpx","display":"inline-block"}'>
					  <text class="icon iconfont icon-chakan9" :style='{"margin":"0 4rpx 0 0","lineHeight":"1.5","fontSize":"24rpx","color":"#fff"}'></text>
					  <text :style='{"color":"#fff","lineHeight":"1.5","fontSize":"24rpx"}'>{{item.clicknum}}</text>
					</view>
				</view>
			  </view>
			  <view @tap="onNewsDetailTap(item.id)" v-if="index>0" class="list-item" :style='{"padding":"20rpx 0","borderColor":"#ccc","margin":"0 0 20rpx 0","flexWrap":"wrap","borderWidth":"0 0 2rpx 0","display":"flex","width":"100%","borderStyle":"solid","height":"auto"}'>
				<view :style='{"width":"100%","padding":"0 20rpx","lineHeight":"1.5","fontSize":"28rpx","color":"#333"}' class="title">{{item.title}}</view>
				<view :style='{"padding":"0 20rpx","margin":"0","overflow":"hidden","color":"#666","width":"100%","lineHeight":"40rpx","fontSize":"28rpx","height":"80rpx"}' class="text">{{item.introduction}}</view>
				<view :style='{"padding":"0 20rpx","order":"2"}'>
				  <text class="icon iconfont icon-shijian21" :style='{"margin":"0 4rpx 0 0","lineHeight":"1.5","fontSize":"24rpx","color":"#666"}'></text>
				  <text :style='{"color":"#666","lineHeight":"1.5","fontSize":"24rpx"}'>{{item.addtime}}</text>
				</view>
				<view :style='{"padding":"0 20rpx"}'>
				  <text class="icon iconfont icon-geren16" :style='{"margin":"0 4rpx 0 0","lineHeight":"1.5","fontSize":"24rpx","color":"#666"}'></text>
				  <text :style='{"color":"#666","lineHeight":"1.5","fontSize":"24rpx"}'>{{item.name}}</text>
				</view>
				<view :style='{"padding":"0 20rpx","display":"inline-block"}'>
				  <text class="icon iconfont icon-zan10" :style='{"margin":"0 4rpx 0 0","lineHeight":"1.5","fontSize":"24rpx","color":"#666"}'></text>
				  <text :style='{"color":"#666","lineHeight":"1.5","fontSize":"24rpx"}'>{{item.thumbsupnum}}</text>
				</view>
				<view :style='{"padding":"0 20rpx","display":"inline-block"}'>
				  <text class="icon iconfont icon-shoucang10" :style='{"margin":"0 4rpx 0 0","lineHeight":"1.5","fontSize":"24rpx","color":"#666"}'></text>
				  <text :style='{"color":"#666","lineHeight":"1.5","fontSize":"24rpx"}'>{{item.storeupnum}}</text>
				</view>
				<view :style='{"padding":"0 20rpx","display":"inline-block"}'>
				  <text class="icon iconfont icon-chakan9" :style='{"margin":"0 4rpx 0 0","lineHeight":"1.5","fontSize":"24rpx","color":"#666"}'></text>
				  <text :style='{"color":"#666","lineHeight":"1.5","fontSize":"24rpx"}'>{{item.clicknum}}</text>
				</view>
			  </view>
			</block>
		  </view>
		</view>
		<!-- 新闻资讯 -->
	</view>
</view>
</template>

<script>
    import menu from '@/utils/menu'
	import '@/assets/css/global-restaurant.css'
	import uniIcons from "@/components/uni-ui/lib/uni-icons/uni-icons.vue"
	export default {
		components: {
			uniIcons
		},
		data() {
			return {
				options2: {
					effect: 'flip',
					loop : true
				},
				options3: {
					effect: 'cube',
					loop : true,
					cubeEffect: {
						shadow: true,
						slideShadows: true,
						shadowOffset: 20,
						shadowScale: 0.94,
					}
				},
				rows: 2,
				column: 4,
				iconArr: [
				  'cuIcon-same',
				  'cuIcon-deliver',
				  'cuIcon-evaluate',
				  'cuIcon-shop',
				  'cuIcon-ticket',
				  'cuIcon-cascades',
				  'cuIcon-discover',
				  'cuIcon-question',
				  'cuIcon-pic',
				  'cuIcon-filter',
				  'cuIcon-footprint',
				  'cuIcon-pulldown',
				  'cuIcon-pullup',
				  'cuIcon-moreandroid',
				  'cuIcon-refund',
				  'cuIcon-qrcode',
				  'cuIcon-remind',
				  'cuIcon-profile',
				  'cuIcon-home',
				  'cuIcon-message',
				  'cuIcon-link',
				  'cuIcon-lock',
				  'cuIcon-unlock',
				  'cuIcon-vip',
				  'cuIcon-weibo',
				  'cuIcon-activity',
				  'cuIcon-friendadd',
				  'cuIcon-friendfamous',
				  'cuIcon-friend',
				  'cuIcon-goods',
				  'cuIcon-selection'
				],
                role : '',
                menuList: [],
                swiperMenuList:[],
                user: {},
                tableName:'',

				//轮播
				swiperList: [],
				homezuozhenyishenglist: [],
				news: [],
			}
		},
		watch: {
		},
		mounted() {
		},
		computed: {
			baseUrl() {
				return this.$base.url;
			},
		},
        async onLoad(){
            
        },
		async onShow() {
			this.swiperMenuList = []
			this.role = uni.getStorageSync("appRole");
			let table = uni.getStorageSync("nowTable");
			let res = await this.$api.session(table);
			this.user = res.data;
			this.tableName = table;
			let menus = menu.list();
			this.menuList = menus;
			this.menuList.forEach((item,key) => {
			    if(key==0) {
			        item.frontMenu.forEach((item2,key2) => {
			            if(item2.child[0].buttons.indexOf("查看")>-1) {
			                this.swiperMenuList.push(item2);
			            }
			        })
			    }
			})
            // let res;
			// 轮播图
			let swiperList = []
			res = await this.$api.list('config', {
				page: 1,
				limit: 5
			});
			for (let item of res.data.list) {
				if (item.name.indexOf('picture') >= 0 && item.value && item.value!="" && item.value!=null ) {
					swiperList.push({
						img: item.value,
                        title: item.name,
						url: item.url
					});
				}
			}
			if (swiperList) {
				this.swiperList = swiperList;
			}
			

			this.getRecommendList()
			this.getHomeList()
			this.getNewsList()
		},
		methods: {
			uGetRect(selector, all) {
				return new Promise(resolve => {
					uni.createSelectorQuery()
					.in(this)
					[all ? 'selectAll' : 'select'](selector)
					.boundingClientRect(rect => {
						if (all && Array.isArray(rect) && rect.length) {
							resolve(rect);
						}
						if (!all && rect) {
							resolve(rect);
						}
					})
					.exec();
				});
			},
			cloneData(data) {
				return JSON.parse(JSON.stringify(data));
			},
			newsTabClick2(index){
				this.newsIndex2 = index
				this.getNewsList()
			},
			async getNewsList(){
				let res;
				let params = {
					page: 1,
					limit: 6,
					sort: 'id',
					order: 'desc',
				}
				// 公告信息
				res = await this.$api.list('news', params)
				this.news = res.data.list
			},
			homeTabClick2(index,name){
				this['home' + name + 'Index2'] = index
				this.getHomeList()
			},
			async getHomeList(){
				let res;
				let params;
				params = {
					page:1,
					limit: 6,
				}
				res = await this.$api.list('zuozhenyisheng', params);
				this.homezuozhenyishenglist = res.data.list
			},
			recommendTabClick2(index,name){
				this[name + 'Index2'] = index
				this.getRecommendList()
			},
			async getRecommendList(){
				let res;
				let params;
			},
			//轮播图跳转
			onSwiperTap(e) {
				if(e.url) {
					if (e.url.indexOf('https') != -1) {
						// #ifdef MP-WEIXIN
						uni.navigateTo({
						    url: '../../common/linkOthers/linkOthers?url=' + encodeURIComponent(e.url),
						});
						return false
						// #endif
						window.open(e.url)
					} else {
						this.$utils.jump(e.url)
					}
				}
			},
			// 新闻详情
			onNewsDetailTap(id) {
				this.$utils.jump(`../news-detail/news-detail?id=${id}`)
			},
			// 推荐列表点击详情
			onDetailTap(tableName, id) {
				this.$utils.jump(`../${tableName}/detail?id=${id}`)
			},
			onPageTap(tableName){

				uni.navigateTo({
					url: `../${tableName}/list`,
					fail: function(){
						uni.switchTab({
							url: `../${tableName}/list`
						});
					}
				});
				// this.$utils.jump(`../${tableName}/list`)
			},
            onPageTap2(index) {
				let url = '../' + index + '/list'
				if(index=='forum'){
					url = '../forum-index/forum-index'
				}
                uni.setStorageSync("useridTag",0);
                uni.navigateTo({
                    url: url,
                    fail: function() {
                        uni.switchTab({
                            url: url
                        });
                    }
                });
            }
		}
	}
</script>

<style lang="scss" scoped>
	.content {
		min-height: calc(100vh - 44px);
		box-sizing: border-box;
	}

	.medical-swiper {
		position: relative;
		transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
		overflow: hidden;

		&::before {
			content: '';
			position: absolute;
			top: -2rpx;
			left: -2rpx;
			right: -2rpx;
			bottom: -2rpx;
			background: linear-gradient(45deg, #3B82F6, #1E40AF, #3B82F6);
			border-radius: 26rpx;
			z-index: -1;
			opacity: 0;
			transition: opacity 0.3s ease;
		}

		&:hover {
			transform: translateY(-4rpx);
			box-shadow: 0 20rpx 60rpx rgba(59, 130, 246, 0.25) !important;

			&::before {
				opacity: 1;
			}
		}

		// 自定义指示器样式
		/deep/ .uni-swiper-dot {
			width: 20rpx !important;
			height: 20rpx !important;
			border-radius: 10rpx !important;
			margin: 0 10rpx !important;
			transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1) !important;
			border: 3rpx solid rgba(255, 255, 255, 0.9) !important;
			background: rgba(255, 255, 255, 0.3) !important;
			box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1) !important;
		}

		/deep/ .uni-swiper-dot-active {
			width: 40rpx !important;
			border-radius: 20rpx !important;
			background: linear-gradient(135deg, #3B82F6 0%, #1E40AF 100%) !important;
			border: 3rpx solid #fff !important;
			box-shadow: 0 4rpx 16rpx rgba(59, 130, 246, 0.5) !important;
			transform: scale(1.1) !important;
		}

		// 轮播图容器动画
		swiper-item {
			transition: all 0.4s ease;
			position: relative;

			&::after {
				content: '';
				position: absolute;
				top: 0;
				left: 0;
				right: 0;
				bottom: 0;
				background: linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, transparent 100%);
				opacity: 0;
				transition: opacity 0.3s ease;
				pointer-events: none;
				border-radius: 21rpx;
			}

			image {
				transition: transform 0.6s cubic-bezier(0.4, 0, 0.2, 1);
			}

			&:active {
				&::after {
					opacity: 1;
				}

				image {
					transform: scale(1.05);
				}
			}
		}

		// 医疗标签样式优化
		.medical-tag {
			backdrop-filter: blur(15rpx);
			-webkit-backdrop-filter: blur(15rpx);
			animation: slideInRight 0.8s cubic-bezier(0.4, 0, 0.2, 1);
			position: relative;
			overflow: hidden;

			&::before {
				content: '';
				position: absolute;
				top: 0;
				left: -100%;
				width: 100%;
				height: 100%;
				background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
				transition: left 0.6s ease;
			}

			&:hover::before {
				left: 100%;
			}
		}

		// 标题文字动画
		.swiper-title {
			animation: slideInUp 1s cubic-bezier(0.4, 0, 0.2, 1);
			position: relative;

			&::after {
				content: '';
				position: absolute;
				bottom: -4rpx;
				left: 0;
				width: 0;
				height: 3rpx;
				background: linear-gradient(90deg, #3B82F6, #1E40AF);
				transition: width 0.6s ease;
			}

			&:hover::after {
				width: 100%;
			}
		}
	}

	// 动画定义
	@keyframes slideInRight {
		0% {
			opacity: 0;
			transform: translateX(50rpx) scale(0.8);
		}
		60% {
			opacity: 0.8;
			transform: translateX(-10rpx) scale(1.05);
		}
		100% {
			opacity: 1;
			transform: translateX(0) scale(1);
		}
	}

	@keyframes slideInUp {
		0% {
			opacity: 0;
			transform: translateY(40rpx) scale(0.9);
		}
		60% {
			opacity: 0.8;
			transform: translateY(-8rpx) scale(1.02);
		}
		100% {
			opacity: 1;
			transform: translateY(0) scale(1);
		}
	}

	@keyframes fadeInRight {
		from {
			opacity: 0;
			transform: translateX(30rpx);
		}
		to {
			opacity: 1;
			transform: translateX(0);
		}
	}

	@keyframes fadeInUp {
		from {
			opacity: 0;
			transform: translateY(30rpx);
		}
		to {
			opacity: 1;
			transform: translateY(0);
		}
	}

	@keyframes pulse {
		0%, 100% {
			transform: scale(1);
		}
		50% {
			transform: scale(1.05);
		}
	}

	@keyframes shimmer {
		0% {
			background-position: -200% 0;
		}
		100% {
			background-position: 200% 0;
		}
	}

	// 轮播图容器背景优化
	.swiper-container {
		background: linear-gradient(135deg, #E8F4FD 0%, #B3D9F2 100%);
		position: relative;
		border-radius: 30rpx;
		overflow: hidden;

		&::before {
			content: '';
			position: absolute;
			top: 0;
			left: 0;
			right: 0;
			bottom: 0;
			background:
				radial-gradient(circle at 20% 30%, rgba(59, 130, 246, 0.15) 0%, transparent 40%),
				radial-gradient(circle at 80% 70%, rgba(30, 64, 175, 0.1) 0%, transparent 40%),
				linear-gradient(45deg, rgba(59, 130, 246, 0.05) 0%, transparent 100%);
			pointer-events: none;
			animation: backgroundShift 8s ease-in-out infinite;
		}

		&::after {
			content: '';
			position: absolute;
			top: -50%;
			left: -50%;
			width: 200%;
			height: 200%;
			background: conic-gradient(from 0deg, transparent, rgba(59, 130, 246, 0.03), transparent);
			animation: rotate 20s linear infinite;
			pointer-events: none;
		}
	}

	// 标题区域样式
	.swiper-header {
		animation: fadeInDown 0.8s ease;

		.health-icon {
			animation: pulse 2s ease-in-out infinite;
		}

		.hot-badge {
			animation: shimmer 2s ease-in-out infinite;
			background: linear-gradient(90deg, rgba(59, 130, 246, 0.1) 0%, rgba(59, 130, 246, 0.2) 50%, rgba(59, 130, 246, 0.1) 100%);
			background-size: 200% 100%;
		}
	}

	// 底部装饰样式
	.swiper-footer {
		animation: fadeInUp 1s ease 0.5s both;

		.decoration-line {
			position: relative;
			overflow: hidden;

			&::after {
				content: '';
				position: absolute;
				top: 0;
				left: -100%;
				width: 100%;
				height: 100%;
				background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.6), transparent);
				animation: slideRight 3s ease-in-out infinite;
			}
		}

		.slide-hint {
			animation: bounce 2s ease-in-out infinite;
		}
	}

	// 额外动画定义
	@keyframes backgroundShift {
		0%, 100% {
			opacity: 1;
		}
		50% {
			opacity: 0.7;
		}
	}

	@keyframes rotate {
		from {
			transform: rotate(0deg);
		}
		to {
			transform: rotate(360deg);
		}
	}

	@keyframes fadeInDown {
		from {
			opacity: 0;
			transform: translateY(-20rpx);
		}
		to {
			opacity: 1;
			transform: translateY(0);
		}
	}

	@keyframes slideRight {
		0% {
			left: -100%;
		}
		100% {
			left: 100%;
		}
	}

	@keyframes bounce {
		0%, 20%, 50%, 80%, 100% {
			transform: translateY(0);
		}
		40% {
			transform: translateY(-6rpx);
		}
		60% {
			transform: translateY(-3rpx);
		}
	}

</style>
